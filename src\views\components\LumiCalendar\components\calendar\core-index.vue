<template>
  <div class="widget-calendar-wrapper relative w-full flex bg-lumi">
    <!-- left menu-->
    <!-- <LeftMenu :date="externalRequestDate" @calendar:datepicker="dateSelected = $event" ref="leftMenu" style="margin: 0 auto;"> -->
    <!-- <template #closeButton>
          <template v-if="slots.closeButton">
            <span class="inline-flex">
              <component
                :is="slots.closeButton"
                @click.prevent.stop="closeCalendar()"
              />
            </span>
          </template>
loseButton v-else @tap="closeCalendar()" />
template> -->
    <!-- / -->
    <!-- <template #loader>
          <template v-if="slots.loader">
            <span class="inline-flex">
              <component :is="slots.loader" :calendarGotLoading="isLoading" />
            </span>
          </template>
          <Loader v-else-if="isLoading" />
        </template> -->
    <!---->
    <!-- <template #sideEvent>
          <div
            class="side-event-box overflow-y-auto custom-scrll p-1"
            :class="{
              'h-50p': !configs?.nativeDatepicker,
              'below-native-datepicker': configs?.nativeDatepicker,
            }"
          >
            <template v-if="slots.sideEvent">
              <component
                :is="slots.sideEvent"
                :dateSelected="dateSelected"
                :calendarEvents="calendarEvents"
              />
            </template>
            <template v-else>
              <SideEvent :eventDate="dateSelected" />
              <SideEvent
                v-if="
                  nextDate(dateSelected).toLocaleDateString('en-CA') !=
                  dateSelected.toLocaleDateString('en-CA')
                "
                :eventDate="nextDate(dateSelected)"
              />
            </template>
          </div>
        </template> -->
    <!-- </LeftMenu> -->
    <!-- calendar base-->
    <div class="calendar-base w-full grow border border-white bg-lumi pb-0" style="flex: 1">
      <!-- calendar base header -->
      <HeaderComp>
        <!--Arrows-->
        <!-- <Arrows @calendar-arrow:today="leftMenu.datepicked = new Date()"
            @calendar-arrow:left="leftMenu.datepicked = prevDate(dateSelected)"
            @calendar-arrow:right="leftMenu.datepicked = nextDate(dateSelected)" :label="/calendar/i.test(dateLabel(dateSelected))
            ? $t(`${dateLabel(dateSelected)}`)
            : dateLabel(dateSelected)
          " :slots="slots" /> -->
        <!-- Painel de Controle Totalmente Integrado -->
        <div class="calendar-control-panel">
          <div class="integrated-control-widget">
            <!-- Botão Anterior -->
            <button
              @click="navigatePrevious"
              class="widget-nav-btn nav-prev"
              :title="getNavigationTitle('prev')"
            >
              <font-awesome-icon icon="fa-solid fa-chevron-left" />
            </button>

            <!-- Seção Central Integrada -->
            <div class="widget-center-section">
              <!-- Data e Período -->
              <div class="widget-date-section">
                <div v-if="configs?.nativeDatepicker" class="widget-date-info">
                  <NativeDatePicker
                    ref="calendar_date_picker"
                    :value="dateSelected"
                    @changed="dateSelected = $event"
                    class="widget-datepicker"
                  />
                  <div class="widget-period-info">
                    <Transition name="date-text-fade" mode="out-in">
                      <span
                        :key="dateKey"
                        class="widget-period-text"
                        :class="{ 'is-past': isDateBeforeToday }"
                      >
                        {{ getRelativeDateText() }}
                      </span>
                    </Transition>
                  </div>
                </div>
              </div>

              <!-- Toggle de Visualização Integrado -->
              <div class="widget-view-modes">
                <button
                  v-for="(mode, index) in ['day', 'week', 'month']"
                  :key="mode"
                  @click="changeView(mode)"
                  class="widget-view-btn"
                  :class="{ 'active': defineView === mode }"
                >
                  <span class="view-label">{{ getViewLabel(mode) }}</span>
                  <span class="view-indicator">{{ getViewIndicator(mode) }}</span>
                </button>
              </div>
            </div>

            <!-- Botão Próximo -->
            <button
              @click="navigateNext"
              class="widget-nav-btn nav-next"
              :title="getNavigationTitle('next')"
            >
              <font-awesome-icon icon="fa-solid fa-chevron-right" />
            </button>
          </div>
        </div>
      </HeaderComp>
      <!--calendar-->
      <div data-widget-item="widget-calendar-comp" class="calendar-wrapper w-full mt-2 overflow-y-auto custom-scrll">
        <!--calendar week-view-->
        <Transition name="calendar-fade" mode="out-in">
          <template v-if="defineView === 'week'">
            <div class="calendar-view-container px-2" :key="'week-' + dateKey">
              <WeekView :weekDays="weekDays" :dateSelected="dateSelected" :dayTimes="dayTimes" :slots="slots" />
            </div>
          </template>
        </Transition>
        <!--calendar day-view-->
        <Transition name="calendar-fade" mode="out-in">
          <template v-if="defineView === 'day'">
            <div class="calendar-view-container px-2" :key="'day-' + dateKey">
              <DayView :dateSelected="dateSelected" :dayTimes="dayTimes" :slots="slots" />
            </div>
          </template>
        </Transition>
        <!--calendar month-view-->
        <Transition name="calendar-fade" mode="out-in">
          <template v-if="defineView === 'month'">
            <div class="calendar-view-container" :key="'month-' + dateKey">
              <MonthView :weekDays="weekDays" :monthDays="monthDays" :dateSelected="dateSelected" :slots="slots" />
            </div>
          </template>
        </Transition>
      </div>
    </div>
    <!---->
  </div>
</template>

<script setup lang="ts">
export interface Props {
  date?: string | Date;
  view?: T_View;
  events?: Appointment[];
  loading?: boolean;
  config?: Configs;
}

// import v-calendar style
import "v-calendar/dist/style.css";

import "../../assets/tailwind.scss";

import {
  onMounted,
  onBeforeMount,
  ref,
  computed,
  watch,
  toRef,
  useSlots,
  type ComponentPublicInstance
} from "vue";
import filters from "../../../../../helpers/filters";
import type { Ref } from "vue";
import LeftMenu from "./left-menu.vue";
import HeaderComp from "./calendar-base-header.vue";
import Arrows from "./calendar-arrows.vue";
import Search from "./calendar-search.vue";
import Toggle from "./view-toggle.vue";
import Loader from "./assets/loader-widget.vue";
import CloseButton from "./close-button.vue";
import NativeDatePicker from "./calendar-native-datepicker.vue";
import { useEventsStore, DEFAULT_CONFIGS } from "../../stores/events";
import type { Appointment, Configs, T_View } from "../../stores/events";

import MonthView from "./calendar-month-view.vue";
import DayView from "./calendar-day-view.vue";
import WeekView from "./calendar-week-view.vue";
import SideEvent from "./calendar-side-event.vue";

import {
  dateLabel,
  twoDigit,
  incrementTime,
  fixDateTime,
  randomId,
  dayName,
  copyDate,
  isoStringToDate,
  dateToIsoString,
  getWeekInterval,
  weekGenerator,
  monthGenerator,
  prevDate,
  nextDate,
} from "./common";

type T_Toggle = typeof Toggle;
type T_LeftMenu = typeof LeftMenu;

const props = withDefaults(defineProps<Props>(), {
  date: undefined,
  view: "week",
  events: () => [],
  loading: false,
  config: () => ({ ...DEFAULT_CONFIGS }),
});

const emit = defineEmits(["calendarClosed", "fetchEvents", "dateSelected"]);

const store = useEventsStore();

const leftMenu: Ref<ComponentPublicInstance<T_LeftMenu>> = ref<
  ComponentPublicInstance<T_LeftMenu>
>() as Ref<ComponentPublicInstance<T_LeftMenu>>;
const viewToggle: Ref<ComponentPublicInstance<T_Toggle>> = ref<
  ComponentPublicInstance<T_Toggle>
>() as Ref<ComponentPublicInstance<T_Toggle>>;
const dateSelected: Ref<Date> = ref(new Date());
const weekDays: Ref<Date[]> = ref([]);
const dayTimes: Ref<string[]> = ref([]);
const defineView: Ref<T_View> = ref(props.view);
const externalRequestDate: Ref<Date | undefined> = ref(undefined);
const monthDays: Ref<Date[]> = ref([]);
const monthDates: Ref<{ start: Date | string; end: Date | string }> = ref({
  start: "",
  end: "",
});
const calendarEvents = computed<Appointment[]>(() => store.getEvents);
const configs = computed<Configs>(() => store.getConfigs);
const isLoading: Ref<boolean> = ref(props.loading);
const slots = useSlots();

// Verificar se a data selecionada é hoje
const isToday = computed(() => {
  // Criar datas para comparação no fuso horário local
  const today = new Date();
  const selectedDate = new Date(dateSelected.value);

  // Resetar horas para comparar apenas as datas
  today.setHours(0, 0, 0, 0);
  selectedDate.setHours(0, 0, 0, 0);

  // Comparar os timestamps
  return selectedDate.getTime() === today.getTime();
});

// Chave única para forçar a recriação do componente quando a data mudar
const dateKey = computed(() => {
  const date = new Date(dateSelected.value);
  return `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;
});

/**
 * closeCalendar
 */
const closeCalendar = (): void => {
  emit("calendarClosed");
};

/**
 * runSearch
 * search by event
 * @param value {string}
 */
const runSearch = async (value: string): Promise<void> => {
  const _s = new RegExp(value, "i");
  let _search = [];
  //
  if (!value.replace(/\s/g, "").length) {
    store.setEvents(props.events);
    return void 0;
  }
  //
  isLoading.value = true;
  _search = calendarEvents.value.filter((rdv: any) => {
    try {
      return _s.test(`${rdv.name}`) || _s.test(`${rdv.keywords}`);
    } catch (e) {
      return false;
    }
  });
  isLoading.value = false;
  if (_search.length !== 0) store.setEvents(_search);
};

/**
 * fetch Appointments
 */
const fetchAppointments = (): void => {
  // fetch appointments from server
  emit("fetchEvents", {
    start: dateToIsoString(
      fixDateTime(monthDates.value.start as Date, "00:00")
    ),
    end: dateToIsoString(fixDateTime(monthDates.value.end as Date, "23:59")),
  });
};

/**
 * verifyFirst Props
 */
const verifyFirstBind = (): void => {
  // date
  if (props.date) {
    if (typeof props.date === 'string') {
      // Se for string, converter para Date
      const b = isoStringToDate(props.date);
      if (b.getTime()) {
        dateSelected.value = b;
        externalRequestDate.value = dateSelected.value;
      }
    } else if (props.date instanceof Date) {
      // Se já for um objeto Date, usar diretamente
      dateSelected.value = new Date(props.date);
      externalRequestDate.value = dateSelected.value;
    }
  }

  // events
  store.setEvents(props.events);
  // config
  store.setConfigs(props.config);
};

/**
 * generateDayTimes
 */
const generateDayTimes = (): void => {
  // dayTimes generation from 08h00 to 23h00
  const _p1 = Array.from(
    { length: 18 - 8 + 1 },
    (_, i) => `${twoDigit(i + 8)}:${twoDigit(0)}`
  );
  //dayTimes generation from 07h00 to 23h59
  // const _p2 = Array.from(
  //   { length: 7 - 0 + 1 },
  //   (_, i) => `${twoDigit(i + 0)}:${twoDigit(0)}`
  // );
  // dayTimes.value = _p1.concat(_p2);
  dayTimes.value = _p1
};

/**
 * watch dateSelected to change everything
 */
watch(dateSelected, () => {
  //refresh week days'date
  weekDays.value = weekGenerator(
    getWeekInterval(dateSelected.value, configs.value.firstDayOfWeek)
  );
  //refresh month days'date
  const __m = monthGenerator(dateSelected.value, configs.value.firstDayOfWeek);
  monthDays.value = __m._days;
  //month date start & end
  monthDates.value = {
    start: __m.firstDay,
    end: __m.lastDay,
  };
  // fetch appointments
  fetchAppointments();

  // A função isDateBeforeToday é um computed e será reavaliada automaticamente

  emit("dateSelected", dateSelected.value)
});

/**
 * watch props and set needed in store
 */
watch(props, () => {
  store.setEvents(props.events);
  store.setConfigs(props.config);

  isLoading.value = props.loading;
});

onBeforeMount(async () => {
  generateDayTimes();
});

/**
 * Navegar para o dia/semana/mês anterior
 */
const navigatePrevious = (): void => {
  const currentView = defineView.value;

  if (currentView === 'day') {
    // Ir para o dia anterior
    const prevDay = new Date(dateSelected.value);
    prevDay.setDate(prevDay.getDate() - 1);
    dateSelected.value = prevDay;
  } else if (currentView === 'week') {
    // Ir para a semana anterior
    const prevWeek = new Date(dateSelected.value);
    prevWeek.setDate(prevWeek.getDate() - 7);
    dateSelected.value = prevWeek;
  } else if (currentView === 'month') {
    // Ir para o mês anterior
    const prevMonth = new Date(dateSelected.value);
    prevMonth.setMonth(prevMonth.getMonth() - 1);
    dateSelected.value = prevMonth;
  }
};

/**
 * Navegar para o próximo dia/semana/mês
 */
const navigateNext = (): void => {
  const currentView = defineView.value;

  if (currentView === 'day') {
    // Ir para o próximo dia
    const nextDay = new Date(dateSelected.value);
    nextDay.setDate(nextDay.getDate() + 1);
    dateSelected.value = nextDay;
  } else if (currentView === 'week') {
    // Ir para a próxima semana
    const nextWeek = new Date(dateSelected.value);
    nextWeek.setDate(nextWeek.getDate() + 7);
    dateSelected.value = nextWeek;
  } else if (currentView === 'month') {
    // Ir para o próximo mês
    const nextMonth = new Date(dateSelected.value);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    dateSelected.value = nextMonth;
  }
};

/**
 * Voltar para hoje
 */
const goToToday = (): void => {
  dateSelected.value = new Date();
};

/**
 * Obter o título para os botões de navegação
 */
const getNavigationTitle = (direction: 'prev' | 'next'): string => {
  const action = direction === 'prev' ? 'Anterior' : 'Próximo';

  if (defineView.value === 'day') {
    return `${action} Dia`;
  } else if (defineView.value === 'week') {
    return `${action} Semana`;
  } else {
    return `${action} Mês`;
  }
};

/**
 * Alterar visualização do widget integrado
 */
const changeView = (view: string) => {
  defineView.value = view as any;
};

/**
 * Obter label do modo de visualização
 */
const getViewLabel = (mode: string): string => {
  const labels = {
    day: 'DIA',
    week: 'SEM',
    month: 'MÊS'
  };
  return labels[mode] || mode.toUpperCase();
};

/**
 * Obter indicador do modo de visualização
 */
const getViewIndicator = (mode: string): string => {
  const currentDate = new Date(dateSelected.value);

  if (mode === 'day') {
    return currentDate.getDate().toString();
  } else if (mode === 'week') {
    // Calcular número da semana do mês
    const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const weekNumber = Math.ceil((currentDate.getDate() + firstDay.getDay()) / 7);
    return weekNumber.toString();
  } else {
    // Mês abreviado
    const months = ['JAN', 'FEV', 'MAR', 'ABR', 'MAI', 'JUN', 'JUL', 'AGO', 'SET', 'OUT', 'NOV', 'DEZ'];
    return months[currentDate.getMonth()];
  }
};

/**
 * Obter o texto relativo à data selecionada
 */
const getRelativeDateText = (): string => {
  // Criar datas para comparação no fuso horário local
  const today = new Date();
  const selectedDate = new Date(dateSelected.value);

  // Resetar horas para comparar apenas as datas
  today.setHours(0, 0, 0, 0);
  selectedDate.setHours(0, 0, 0, 0);

  // Verificar se é hoje
  if (selectedDate.getTime() === today.getTime()) {
    return 'hoje';
  }

  // Verificar se é amanhã
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  if (selectedDate.getTime() === tomorrow.getTime()) {
    return 'amanhã';
  }

  // Verificar se é ontem
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  if (selectedDate.getTime() === yesterday.getTime()) {
    return 'ontem';
  }

  // Para outras datas, usar o filtro howMuchTime
  return filters.howMuchTime(selectedDate, {
    type: 'date',
    compareTo: today,
    prefix: true
  });
};

/**
 * Verificar se a data selecionada é anterior a hoje
 */
const isDateBeforeToday = computed(() => {
  // Criar datas para comparação no fuso horário local
  const today = new Date();
  const selectedDate = new Date(dateSelected.value);

  // Resetar horas para comparar apenas as datas
  today.setHours(0, 0, 0, 0);
  selectedDate.setHours(0, 0, 0, 0);

  // Verificar se a data selecionada é anterior a hoje
  return selectedDate.getTime() < today.getTime();
});



onMounted(async () => {
  // verify first bind props: date, events
  verifyFirstBind();
});
</script>

<style lang="scss" scoped>
.calendar-wrapper {
  height: calc(83vh - 66px);
}

.side-event-box {
  &.below-native-datepicker {
    height: calc(100% - 92px);
  }
}

/* Painel de Controle Totalmente Integrado */
.calendar-control-panel {
  width: 100%;
  padding: 1rem 1.5rem;
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
}

.integrated-control-widget {
  display: flex;
  align-items: center;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 8px;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  max-width: 600px;
  width: 100%;
}

.widget-nav-btn {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: #ffffff;
  color: #64748b;
  border: 1px solid #e2e8f0;
  transition: all 0.15s ease;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  flex-shrink: 0;
}

.widget-nav-btn:hover {
  background: #f1f5f9;
  color: #475569;
  border-color: #cbd5e1;
}

.widget-center-section {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
  background: #ffffff;
  border-radius: 8px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
}

.widget-date-section {
  flex: 1;
  min-width: 0;
}

.widget-date-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.widget-datepicker {
  width: 100%;
  max-width: 180px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
}

.widget-period-info {
  margin-top: 2px;
}

.widget-period-text {
  font-size: 11px;
  font-weight: 500;
  color: #64748b;
  text-transform: lowercase;
  transition: all 0.2s ease;
  text-align: center;
}

.widget-period-text.is-past {
  color: #f97316;
  font-weight: 600;
}

.widget-view-modes {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.widget-view-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 6px;
  background: transparent;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.15s ease;
  min-width: 40px;
  height: 36px;
}

.widget-view-btn:hover {
  background: #f1f5f9;
  border-color: #e2e8f0;
}

.widget-view-btn.active {
  background: #0ea5e9;
  color: white;
  border-color: #0284c7;
}

.view-label {
  font-size: 9px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1;
}

.view-indicator {
  font-size: 12px;
  font-weight: 700;
  line-height: 1;
  margin-top: 1px;
}



/* Botões de Navegação Flat */
.nav-btn {
  width: 36px;
  height: 36px;
  min-width: 36px;
  min-height: 36px;
  border-radius: 6px;
  background: transparent;
  color: #64748b;
  border: 1px solid #e2e8f0;
  transition: all 0.15s ease;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
  outline: none;
}

.nav-btn:hover {
  background: #f8fafc;
  color: #475569;
  border-color: #cbd5e1;
}

.nav-btn:active {
  background: #f1f5f9;
  transform: scale(0.98);
}

.nav-btn:focus {
  border-color: #0ea5e9;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
}

.today-button {
  height: calc(100% - 2px);
  padding: 0 12px;
  margin: 1px;
  border-radius: 0 6px 6px 0;
  background: linear-gradient(to right, rgba(255,255,255,0), #f0f9ff);
  color: #0ea5e9;
  font-weight: 600;
  font-size: 13px;
  transition: all 0.2s ease;
}

.today-button:hover {
  background: linear-gradient(to right, rgba(255,255,255,0), #e0f2fe);
  color: #0284c7;
}

.today-text {
  padding-right: 4px;
}

/* Responsividade */
@media (max-width: 768px) {
  .calendar-control-panel {
    padding: 0.75rem 1rem;
  }

  .integrated-control-widget {
    max-width: 100%;
    padding: 6px;
    gap: 6px;
  }

  .widget-center-section {
    flex-direction: column;
    gap: 8px;
    padding: 6px 8px;
  }

  .widget-date-section {
    width: 100%;
  }

  .widget-view-modes {
    justify-content: center;
    width: 100%;
  }

  .widget-nav-btn {
    width: 32px;
    height: 32px;
    font-size: 11px;
  }

  .widget-view-btn {
    min-width: 36px;
    height: 32px;
  }
}

@media (max-width: 480px) {
  .calendar-control-panel {
    padding: 0.5rem 0.75rem;
  }

  .integrated-control-widget {
    padding: 4px;
    gap: 4px;
  }

  .widget-center-section {
    padding: 4px 6px;
    gap: 6px;
  }

  .widget-nav-btn {
    width: 28px;
    height: 28px;
    font-size: 10px;
  }

  .widget-datepicker {
    max-width: 160px;
    font-size: 13px;
  }

  .widget-view-btn {
    min-width: 32px;
    height: 28px;
    padding: 2px 6px;
  }

  .view-label {
    font-size: 8px;
  }

  .view-indicator {
    font-size: 11px;
  }
}

/* Animações para transição de data */
.calendar-fade-enter-active,
.calendar-fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.calendar-fade-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.calendar-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Animações para o texto relativo à data */
.date-text-fade-enter-active,
.date-text-fade-leave-active {
  transition: opacity 0.25s ease, transform 0.25s ease;
}

.date-text-fade-enter-from {
  opacity: 0;
  transform: translateY(5px);
}

.date-text-fade-leave-to {
  opacity: 0;
  transform: translateY(-5px);
}
</style>
